# Ensure all Pydantic models are properly rebuilt to avoid circular dependency issues
def rebuild_models():
    """Rebuild all Pydantic models to resolve forward references"""
    try:
        from .schemas import (
            PaymentCreateRequest, PaymentCreateResponse, AntomSessionRequest, AntomSessionResponse,
            PaymentStatusRequest, PaymentStatusResponse, PaymentHistoryResponse,
            AntomCallbackRequest, AntomCallbackResponse,
            SubscriptionUpdateRequest, SubscriptionUpdateResponse,
            NotifyResult, NotifyResponse
        )

        # Rebuild all models
        models_to_rebuild = [
            PaymentCreateRequest, PaymentCreateResponse, AntomSessionRequest, AntomSessionResponse,
            PaymentStatusRequest, PaymentStatusResponse, PaymentHistoryResponse,
            AntomCallbackRequest, AntomCallbackResponse,
            SubscriptionUpdateRequest, SubscriptionUpdateResponse,
            NotifyResult, NotifyResponse
        ]

        for model in models_to_rebuild:
            try:
                model.model_rebuild()
            except Exception:
                pass  # Ignore rebuild errors

    except ImportError:
        pass  # Models not yet available

# Call rebuild when the module is imported
rebuild_models()